# DTXManiaCX Song Selection UI Implementation Plan

## Overview

This document outlines the implementation plan for updating DTXManiaCX's song selection UI to match the authentic DTXManiaNX curved layout system. The current implementation uses a basic list layout, but DTXManiaNX uses a distinctive curved positioning system that is core to the DTXMania experience.

## Current vs. Target Implementation

### Current DTXManiaCX Implementation
- Basic vertical list layout with 13 visible items
- Simple linear positioning (center selection at item 6)
- Basic scrolling animation
- Generic UI component approach

### Target DTXManiaNX Implementation  
- **Curved layout system** with exact coordinate positioning
- **13 song bars** at specific (X,Y) coordinates forming a curve
- **Bar 5 (center)** is always the selected song at (464, 270)
- **Different bar types** (Score, Box, Other) with separate graphics
- **Advanced animation system** with acceleration curves

## Key Implementation Requirements

### 1. Curved Layout Coordinates (Critical)

Must implement the exact `ptバーの基本座標` coordinate system:

```csharp
private readonly Point[] CurvedBarCoordinates = new Point[] { 
    new Point(708, 5),      // Bar 0 (top)
    new Point(626, 56),     // Bar 1
    new Point(578, 107),    // Bar 2  
    new Point(546, 158),    // Bar 3
    new Point(528, 209),    // Bar 4
    new Point(464, 270),    // Bar 5 (CENTER/SELECTED) ← KEY POSITION
    new Point(548, 362),    // Bar 6
    new Point(578, 413),    // Bar 7
    new Point(624, 464),    // Bar 
    new Point(686, 515),    // Bar 9
    new Point(788, 566),    // Bar 10
    new Point(996, 617),    // Bar 11
    new Point(1280, 668)    // Bar 12 (bottom)
};
```

**Visual Pattern**:
- Bars 0-4: Curve inward toward center (decreasing X)
- Bar 5: Center/selected position (leftmost X = 464)
- Bars 6-12: Curve outward from center (increasing X)

### 2. Song Bar Types and Graphics

Must support different bar types with corresponding graphics:

```csharp
public enum BarType
{
    Score,      // Regular songs
    Box,        // Folders/directories  
    Other       // Random, back navigation, etc.
}

// Required graphics resources:
// Graphics/5_bar score.png (normal song bar)
// Graphics/5_bar score selected.png (selected song bar)
// Graphics/5_bar box.png (normal folder bar)
// Graphics/5_bar box selected.png (selected folder bar)
// Graphics/5_bar other.png (normal other bar)
// Graphics/5_bar other selected.png (selected other bar)
```

### 3. Bar Information Structure

Each visible bar needs complete information:

```csharp
public class SongBarInfo
{
    public BarType BarType { get; set; }
    public string TitleString { get; set; }
    public ITexture TitleTexture { get; set; }
    public Color TextColor { get; set; }
    public ITexture PreviewImage { get; set; }
    public ITexture ClearLamp { get; set; }
    public SongListNode SongNode { get; set; }
    public int DifficultyLevel { get; set; }
    public bool IsSelected { get; set; }
}
```

### 4. Animation System

DTXManiaNX uses sophisticated scrolling animation:

```csharp
// Scroll acceleration based on distance to target
private int GetScrollAcceleration(int distance)
{
    if (distance <= 100) return 2;
    else if (distance <= 300) return 3;
    else if (distance <= 500) return 4;
    else return 8;
}

// Target-based animation
private void UpdateScrollAnimation(double deltaTime)
{
    // Move currentScrollCounter toward targetScrollCounter
    // Speed increases with distance for responsive feel
}
```

### 5. Component Architecture

Must match DTXManiaNX component structure:

```csharp
// Main stage coordinates all components
public class SongSelectionStage : BaseStage
{
    private CurvedSongListDisplay _songListDisplay;     // Curved layout
    private SongStatusPanel _statusPanel;               // Right panel info
    private PreviewImagePanel _previewPanel;            // Preview content
    private PreviewSoundManager _previewSound;          // Audio preview
    private ArtistCommentDisplay _artistComment;        // Artist info
    private QuickConfigPanel _quickConfig;              // Settings overlay
}
```

## Implementation Tasks

### Phase 1: Core Curved Layout System

1. **Update SongListDisplay for Curved Layout**
   - Replace linear positioning with curved coordinates
   - Implement 13-bar system with center selection (bar 5)
   - Add proper bar type support (Score/Box/Other)

2. **Implement DTXManiaNX Animation System**
   - Target-based scrolling with acceleration
   - Smooth movement between curved positions
   - Bar regeneration during scroll

3. **Add Graphics Resource Support**
   - Load bar type graphics (score, box, other)
   - Support selected vs normal bar graphics
   - Background panels (header, footer, background)

### Phase 2: Bar Generation and Rendering

1. **Enhanced Song Bar Rendering**
   - Generate title textures with proper fonts
   - Load and scale preview images
   - Create clear lamp indicators
   - Support multiple bar types

2. **Bar Information Management**
   - Cache bar information for performance
   - Handle bar state changes (selected/normal)
   - Support difficulty-specific data

### Phase 3: Component Integration

1. **Update SongSelectionStage**
   - Load background graphics (5_background.jpg, panels)
   - Coordinate multiple UI components
   - Handle component communication

2. **Status Panel Enhancement**
   - Position relative to curved song list
   - Show all difficulties simultaneously
   - Real-time updates with song selection

### Phase 4: Polish and Optimization

1. **Performance Optimization**
   - Efficient texture caching
   - Minimize bar regeneration
   - Smooth 60fps animation

2. **Visual Polish**
   - Authentic DTXMania styling
   - Proper fade effects
   - Responsive visual feedback

## Technical Considerations

### Coordinate System
- DTXManiaNX uses absolute pixel coordinates
- Must account for different screen resolutions
- Consider scaling for modern displays

### Resource Management  
- Efficient texture loading and caching
- Proper disposal of generated graphics
- Memory management for large song lists

### Performance
- Target 60fps smooth animation
- Minimize texture generation during scrolling
- Efficient font rendering

### Compatibility
- Maintain existing song management system
- Preserve configuration and save data
- Support existing input handling

## Success Criteria

1. **Visual Authenticity**: Layout matches DTXManiaNX exactly
2. **Smooth Animation**: 60fps scrolling with proper acceleration
3. **Functional Parity**: All navigation and selection features work
4. **Performance**: No lag during scrolling or song changes
5. **Resource Efficiency**: Proper memory and texture management

## Implementation Priority

**CRITICAL**: The curved layout coordinate system is the most important aspect. Without the exact `ptバーの基本座標` positioning, the interface will not feel authentic to DTXMania users.

**HIGH**: Animation system and bar type support
**MEDIUM**: Graphics resource loading and caching
**LOW**: Visual polish and optimization

This implementation will transform DTXManiaCX's song selection from a generic list interface into the authentic curved DTXMania experience that users expect.